@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式 - 简化版本 */
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* 修复水合不匹配：明确设置 overscroll 行为 */
  overscroll-behavior-x: none;
}

/* 矩阵专用样式 */
.matrix-viewport {
  /* 视口容器样式 */
  position: relative;
  will-change: scroll-position;
  contain: layout style paint;
}

.matrix-viewport::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.matrix-viewport::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.matrix-viewport::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.matrix-viewport::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.matrix-container {
  /* 矩阵容器样式 */
  will-change: transform;
  contain: layout style paint;
}

.matrix-cell {
  transition: all 0.1s ease;
  box-sizing: border-box;
  user-select: none;
  cursor: pointer;
}

.matrix-cell:hover {
  transform: scale(1.05);
  z-index: 10;
}

.matrix-cell.selected {
  box-shadow: 0 0 0 2px #3b82f6;
}

.matrix-cell.coordinate-mode {
  font-family: 'Monaco', 'Menlo', monospace;
}

.matrix-cell.color-mode {
  font-weight: bold;
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
}

.matrix-cell.level-mode {
  font-family: 'Arial', sans-serif;
  font-weight: bold;
}

.matrix-cell.word-mode {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  line-height: 1.2;
}

.matrix-loading {
  /* 加载状态样式 */
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}