/**
 * 矩阵视口管理Hook
 * 🎯 核心价值：动态计算格子尺寸，支持自适应放大和滚动
 * 📦 功能范围：视口尺寸监听、格子尺寸计算、滚动状态管理
 * 🔄 架构设计：响应式设计，自动适配不同屏幕尺寸
 */

import { useCallback, useEffect, useRef, useState } from 'react';
import type { ViewportConfig, ScaleConfig } from '@/core/matrix/MatrixTypes';
import { MATRIX_SIZE, DEFAULT_SCALE_CONFIG } from '@/core/matrix/MatrixTypes';

interface UseMatrixViewportOptions {
  containerRef: React.RefObject<HTMLElement>;
  scaleConfig?: ScaleConfig;
  onViewportChange?: (viewport: ViewportConfig) => void;
}

interface UseMatrixViewportReturn {
  viewport: ViewportConfig;
  matrixStyle: React.CSSProperties;
  cellStyle: (x: number, y: number) => React.CSSProperties;
  isLoading: boolean;
}

/**
 * 计算最佳格子尺寸
 */
const calculateOptimalCellSize = (
  containerWidth: number,
  containerHeight: number,
  scaleConfig: ScaleConfig
): { cellSize: number; gap: number; scale: number; needsScroll: boolean } => {
  const { minCellSize, maxCellSize, gapRatio } = scaleConfig;
  
  // 计算基于容器尺寸的理想格子大小
  const idealCellSizeByWidth = containerWidth / MATRIX_SIZE;
  const idealCellSizeByHeight = containerHeight / MATRIX_SIZE;
  const idealCellSize = Math.min(idealCellSizeByWidth, idealCellSizeByHeight);
  
  // 应用尺寸限制
  let cellSize = Math.max(minCellSize, Math.min(maxCellSize, idealCellSize));
  
  // 计算间距
  const gap = cellSize * gapRatio;
  
  // 计算实际需要的总尺寸
  const totalSize = (cellSize + gap) * MATRIX_SIZE - gap;
  
  // 判断是否需要滚动
  const needsScroll = totalSize > Math.min(containerWidth, containerHeight);
  
  // 计算缩放比例（相对于默认20px）
  const scale = cellSize / 20;
  
  return { cellSize, gap, scale, needsScroll };
};

/**
 * 矩阵视口管理Hook
 */
export const useMatrixViewport = ({
  containerRef,
  scaleConfig = DEFAULT_SCALE_CONFIG,
  onViewportChange,
}: UseMatrixViewportOptions): UseMatrixViewportReturn => {
  const [viewport, setViewport] = useState<ViewportConfig>({
    width: 0,
    height: 0,
    cellSize: 20,
    gap: 2,
    scale: 1,
    needsScroll: false,
  });
  const [isLoading, setIsLoading] = useState(true);
  const resizeObserverRef = useRef<ResizeObserver | null>(null);

  /**
   * 更新视口配置
   */
  const updateViewport = useCallback((width: number, height: number) => {
    if (!scaleConfig.enabled) {
      // 如果禁用缩放，使用默认尺寸
      const defaultViewport: ViewportConfig = {
        width,
        height,
        cellSize: 20,
        gap: 2,
        scale: 1,
        needsScroll: width < MATRIX_SIZE * 22 || height < MATRIX_SIZE * 22,
      };
      setViewport(defaultViewport);
      onViewportChange?.(defaultViewport);
      return;
    }

    const { cellSize, gap, scale, needsScroll } = calculateOptimalCellSize(
      width,
      height,
      scaleConfig
    );

    const newViewport: ViewportConfig = {
      width,
      height,
      cellSize,
      gap,
      scale,
      needsScroll,
    };

    setViewport(newViewport);
    onViewportChange?.(newViewport);
  }, [scaleConfig, onViewportChange]);

  /**
   * 初始化和监听容器尺寸变化
   */
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // 初始化尺寸
    const rect = container.getBoundingClientRect();
    updateViewport(rect.width, rect.height);
    setIsLoading(false);

    // 创建ResizeObserver监听尺寸变化
    resizeObserverRef.current = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        updateViewport(width, height);
      }
    });

    resizeObserverRef.current.observe(container);

    return () => {
      resizeObserverRef.current?.disconnect();
    };
  }, [containerRef, updateViewport]);

  /**
   * 生成矩阵容器样式
   */
  const matrixStyle: React.CSSProperties = {
    position: 'relative',
    width: `${(viewport.cellSize + viewport.gap) * MATRIX_SIZE - viewport.gap}px`,
    height: `${(viewport.cellSize + viewport.gap) * MATRIX_SIZE - viewport.gap}px`,
    minWidth: `${(viewport.cellSize + viewport.gap) * MATRIX_SIZE - viewport.gap}px`,
    minHeight: `${(viewport.cellSize + viewport.gap) * MATRIX_SIZE - viewport.gap}px`,
  };

  /**
   * 生成单个格子样式
   */
  const cellStyle = useCallback((x: number, y: number): React.CSSProperties => {
    return {
      position: 'absolute',
      left: `${x * (viewport.cellSize + viewport.gap)}px`,
      top: `${y * (viewport.cellSize + viewport.gap)}px`,
      width: `${viewport.cellSize}px`,
      height: `${viewport.cellSize}px`,
      fontSize: `${Math.max(8, viewport.cellSize * 0.4)}px`,
    };
  }, [viewport.cellSize, viewport.gap]);

  return {
    viewport,
    matrixStyle,
    cellStyle,
    isLoading,
  };
};

export default useMatrixViewport;
