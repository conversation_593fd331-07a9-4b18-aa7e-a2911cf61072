/**
 * 矩阵视口功能测试脚本
 * 测试格子自适应放大和滚动功能
 */

import { DEFAULT_SCALE_CONFIG } from '../core/matrix/MatrixTypes';

// 模拟不同的容器尺寸
const testCases = [
  { name: '小屏幕', width: 400, height: 300 },
  { name: '中等屏幕', width: 800, height: 600 },
  { name: '大屏幕', width: 1200, height: 900 },
  { name: '超大屏幕', width: 1920, height: 1080 },
  { name: '正方形', width: 800, height: 800 },
  { name: '窄屏', width: 300, height: 800 },
  { name: '宽屏', width: 1600, height: 400 },
];

// 计算最佳格子尺寸的函数（复制自hook）
const calculateOptimalCellSize = (
  containerWidth: number,
  containerHeight: number,
  scaleConfig = DEFAULT_SCALE_CONFIG
) => {
  const MATRIX_SIZE = 33;
  const { minCellSize, maxCellSize, gapRatio } = scaleConfig;
  
  // 计算基于容器尺寸的理想格子大小
  const idealCellSizeByWidth = containerWidth / MATRIX_SIZE;
  const idealCellSizeByHeight = containerHeight / MATRIX_SIZE;
  const idealCellSize = Math.min(idealCellSizeByWidth, idealCellSizeByHeight);
  
  // 应用尺寸限制
  let cellSize = Math.max(minCellSize, Math.min(maxCellSize, idealCellSize));
  
  // 计算间距
  const gap = cellSize * gapRatio;
  
  // 计算实际需要的总尺寸
  const totalSize = (cellSize + gap) * MATRIX_SIZE - gap;
  
  // 判断是否需要滚动
  const needsScroll = totalSize > Math.min(containerWidth, containerHeight);
  
  // 计算缩放比例（相对于默认20px）
  const scale = cellSize / 20;
  
  return { cellSize, gap, scale, needsScroll, totalSize };
};

// 运行测试
console.log('🧪 矩阵视口功能测试');
console.log('='.repeat(60));

testCases.forEach(({ name, width, height }) => {
  const result = calculateOptimalCellSize(width, height);
  
  console.log(`\n📱 ${name} (${width}x${height})`);
  console.log(`   格子尺寸: ${result.cellSize.toFixed(1)}px`);
  console.log(`   间距: ${result.gap.toFixed(1)}px`);
  console.log(`   缩放比例: ${result.scale.toFixed(2)}x`);
  console.log(`   矩阵总尺寸: ${result.totalSize.toFixed(0)}px`);
  console.log(`   需要滚动: ${result.needsScroll ? '是' : '否'}`);
  
  // 计算利用率
  const utilization = (result.totalSize / Math.min(width, height) * 100).toFixed(1);
  console.log(`   空间利用率: ${utilization}%`);
});

console.log('\n✅ 测试完成');

// 验证边界情况
console.log('\n🔍 边界情况测试');
console.log('-'.repeat(40));

// 极小容器
const tinyResult = calculateOptimalCellSize(100, 100);
console.log(`极小容器 (100x100): 格子${tinyResult.cellSize}px, 滚动${tinyResult.needsScroll ? '是' : '否'}`);

// 极大容器
const hugeResult = calculateOptimalCellSize(3000, 3000);
console.log(`极大容器 (3000x3000): 格子${hugeResult.cellSize}px, 滚动${hugeResult.needsScroll ? '是' : '否'}`);

// 禁用缩放的情况
const noScaleConfig = { ...DEFAULT_SCALE_CONFIG, enabled: false };
console.log('\n🚫 禁用缩放测试');
testCases.slice(0, 3).forEach(({ name, width, height }) => {
  // 模拟禁用缩放的行为
  const defaultCellSize = 20;
  const defaultGap = 2;
  const totalSize = (defaultCellSize + defaultGap) * 33 - defaultGap;
  const needsScroll = width < totalSize || height < totalSize;
  
  console.log(`${name}: 固定20px格子, 滚动${needsScroll ? '是' : '否'}`);
});

export { calculateOptimalCellSize };
