/**
 * 矩阵核心引擎
 * 🎯 核心价值：统一的矩阵处理引擎，数据驱动渲染，业务模式切换
 * 📦 功能范围：数据处理管道、渲染引擎、交互处理、性能优化
 * 🔄 架构设计：基于配置驱动的模块化架构，支持插件式业务模式
 */

import type {
  BusinessMode,
  CellData,
  CellRenderData,
  Coordinate,
  InteractionEvent,
  MatrixConfig,
  MatrixData,
  ModeHandler,
  PerformanceMetrics,
  ProcessedMatrixData,
} from './MatrixTypes';

import {
  DEFAULT_COLOR_VALUES,
  getCachedGroupAData,
  getMatrixDataByCoordinate,
  toDisplayCoordinate
} from '../data/GroupAData';

import { coordinateKey } from './MatrixTypes';

// ===== 样式工具函数 =====

const getBackgroundColor = (cell: CellData, mode: BusinessMode, cellColor?: any): string => {
  if (cell.isSelected) {
    switch (mode) {
      case 'coordinate': return '#e3f2fd';
      case 'color': return '#000000';
      case 'level': return '#fff3cd';
      case 'word': return '#e8f5e8';
      default: return '#ffffff';
    }
  }

  if (mode === 'color' && cellColor && DEFAULT_COLOR_VALUES[cellColor as keyof typeof DEFAULT_COLOR_VALUES]) {
    return DEFAULT_COLOR_VALUES[cellColor as keyof typeof DEFAULT_COLOR_VALUES].hex;
  }

  return '#ffffff';
};

const getTextColor = (cell: CellData, mode: BusinessMode): string => {
  switch (mode) {
    case 'color':
      return cell.color ? '#ffffff' : '#666666';
    case 'level':
      return cell.level ? '#333333' : '#999999';
    case 'word':
      return cell.word ? '#333333' : '#cccccc';
    default:
      return '#333333';
  }
};

const getBorder = (cell: CellData, mode: BusinessMode): string => {
  if (cell.isSelected) {
    return mode === 'coordinate' ? '2px solid #3b82f6' : '2px solid #000000';
  }
  return '1px solid #e0e0e0';
};

const getFontSize = (mode: BusinessMode): string => {
  switch (mode) {
    case 'coordinate': return '12px';
    case 'color': return '10px';
    case 'level': return '14px';
    case 'word': return '11px';
    default: return '12px';
  }
};

const createCellStyle = (cell: CellData, mode: BusinessMode, matrixDataColor?: any) => {
  const cellColor = matrixDataColor || cell.color;
  const style: any = {
    backgroundColor: getBackgroundColor(cell, mode, cellColor),
    border: getBorder(cell, mode),
    color: getTextColor(cell, mode),
    fontSize: getFontSize(mode),
  };

  if (mode === 'color') {
    style.fontWeight = 'bold';
  } else if (mode === 'level') {
    style.fontWeight = cell.level ? 'bold' : 'normal';
  }

  return style;
};

const createCellClassName = (cell: CellData, mode: BusinessMode, matrixDataColor?: any, hasMatrixData?: boolean) => {
  const classes = ['matrix-cell', `${mode}-mode`];

  if (cell.isSelected) classes.push('selected');
  if (cell.isHovered) classes.push('hovered');
  if (cell.isActive) classes.push('active');

  const cellColor = matrixDataColor || cell.color;
  if (mode === 'color' && cellColor) classes.push(`color-${cellColor}`);
  if (mode === 'color' && hasMatrixData) classes.push('has-matrix-data');
  if (mode === 'level' && cell.level) classes.push('has-level');
  if (mode === 'word' && cell.word) classes.push('has-word');

  return classes.join(' ');
};

const createCellContent = (cell: CellData, mode: BusinessMode) => {
  switch (mode) {
    case 'coordinate': {
      // 使用显示坐标而不是网格坐标
      const [displayX, displayY] = toDisplayCoordinate(cell.x, cell.y);
      return `${displayX},${displayY}`;
    }
    case 'color': return '';
    case 'level': return cell.level?.toString() || '';
    case 'word': return cell.word || '';
    default: return '';
  }
};

// ===== 业务模式处理器 =====

/** 坐标模式处理器 */
const coordinateModeHandler: ModeHandler = {
  processData: (data: MatrixData, config: MatrixConfig): ProcessedMatrixData => {
    const renderData = new Map<string, CellRenderData>();

    data.cells.forEach((cell, key) => {
      renderData.set(key, {
        content: createCellContent(cell, 'coordinate'),
        style: createCellStyle(cell, 'coordinate'),
        className: createCellClassName(cell, 'coordinate'),
        isInteractive: true,
      });
    });

    return {
      cells: data.cells,
      renderData,
      metadata: {
        totalCells: data.cells.size,
        activeCells: Array.from(data.cells.values()).filter(cell => cell.isActive).length,
        selectedCells: data.selectedCells.size,
        mode: 'coordinate',
      },
    };
  },

  renderCell: (cell: CellData, config: MatrixConfig): CellRenderData => ({
    content: createCellContent(cell, 'coordinate'),
    style: createCellStyle(cell, 'coordinate'),
    className: createCellClassName(cell, 'coordinate'),
    isInteractive: true,
  }),

  handleInteraction: (event: InteractionEvent, cell: CellData) => {
    console.log(`Coordinate mode interaction: ${event.type} at (${cell.x}, ${cell.y})`);
  },
};

/** 颜色模式处理器 */
const colorModeHandler: ModeHandler = {
  processData: (data: MatrixData, config: MatrixConfig): ProcessedMatrixData => {
    const renderData = new Map<string, CellRenderData>();

    data.cells.forEach((cell, key) => {
      // 检查是否有矩阵数据
      const groupAData = getCachedGroupAData();
      const matrixData = getMatrixDataByCoordinate(groupAData, cell.x, cell.y);
      const cellColor = matrixData?.color || cell.color;

      renderData.set(key, {
        content: createCellContent(cell, 'color'),
        style: createCellStyle(cell, 'color', cellColor),
        className: createCellClassName(cell, 'color', cellColor, !!matrixData),
        isInteractive: true,
      });
    });

    return {
      cells: data.cells,
      renderData,
      metadata: {
        totalCells: data.cells.size,
        activeCells: Array.from(data.cells.values()).filter(cell => cell.isActive).length,
        selectedCells: data.selectedCells.size,
        mode: 'color',
      },
    };
  },

  renderCell: (cell: CellData, config: MatrixConfig): CellRenderData => {
    return {
      content: createCellContent(cell, 'color'),
      style: createCellStyle(cell, 'color'),
      className: createCellClassName(cell, 'color'),
      isInteractive: true,
    };
  },

  handleInteraction: (event: InteractionEvent, cell: CellData) => {
    console.log(`Color mode interaction: ${event.type} at (${cell.x}, ${cell.y}), color: ${cell.color}`);
  },
};

/** 等级模式处理器 */
const levelModeHandler: ModeHandler = {
  processData: (data: MatrixData, config: MatrixConfig): ProcessedMatrixData => {
    const renderData = new Map<string, CellRenderData>();

    data.cells.forEach((cell, key) => {
      renderData.set(key, {
        content: createCellContent(cell, 'level'),
        style: createCellStyle(cell, 'level'),
        className: createCellClassName(cell, 'level'),
        isInteractive: true,
      });
    });

    return {
      cells: data.cells,
      renderData,
      metadata: {
        totalCells: data.cells.size,
        activeCells: Array.from(data.cells.values()).filter(cell => cell.isActive).length,
        selectedCells: data.selectedCells.size,
        mode: 'level',
      },
    };
  },

  renderCell: (cell: CellData, config: MatrixConfig): CellRenderData => ({
    content: createCellContent(cell, 'level'),
    style: createCellStyle(cell, 'level'),
    className: createCellClassName(cell, 'level'),
    isInteractive: true,
  }),

  handleInteraction: (event: InteractionEvent, cell: CellData) => {
    console.log(`Level mode interaction: ${event.type} at (${cell.x}, ${cell.y}), level: ${cell.level}`);
  },
};

/** 词语模式处理器 */
const wordModeHandler: ModeHandler = {
  processData: (data: MatrixData, config: MatrixConfig): ProcessedMatrixData => {
    const renderData = new Map<string, CellRenderData>();

    data.cells.forEach((cell, key) => {
      renderData.set(key, {
        content: createCellContent(cell, 'word'),
        style: createCellStyle(cell, 'word'),
        className: createCellClassName(cell, 'word'),
        isInteractive: true,
      });
    });

    return {
      cells: data.cells,
      renderData,
      metadata: {
        totalCells: data.cells.size,
        activeCells: Array.from(data.cells.values()).filter(cell => cell.isActive).length,
        selectedCells: data.selectedCells.size,
        mode: 'word',
      },
    };
  },

  renderCell: (cell: CellData, config: MatrixConfig): CellRenderData => ({
    content: createCellContent(cell, 'word'),
    style: createCellStyle(cell, 'word'),
    className: createCellClassName(cell, 'word'),
    isInteractive: true,
  }),

  handleInteraction: (event: InteractionEvent, cell: CellData) => {
    console.log(`Word mode interaction: ${event.type} at (${cell.x}, ${cell.y}), word: ${cell.word}`);
  },
};

// ===== 模式处理器注册表 =====

const modeHandlers: Record<BusinessMode, ModeHandler> = {
  coordinate: coordinateModeHandler,
  color: colorModeHandler,
  level: levelModeHandler,
  word: wordModeHandler,
};

// ===== 矩阵核心引擎 =====

export class MatrixCore {
  private performanceMetrics: PerformanceMetrics = {
    renderTime: 0,
    updateTime: 0,
    cacheHitRate: 0,
    memoryUsage: 0,
    frameRate: 60,
  };

  /**
   * 处理矩阵数据
   */
  processData(data: MatrixData, config: MatrixConfig): ProcessedMatrixData {
    const startTime = performance.now();

    const handler = modeHandlers[config.mode];
    if (!handler) {
      throw new Error(`Unknown business mode: ${config.mode}`);
    }

    const result = handler.processData(data, config);

    const endTime = performance.now();
    this.performanceMetrics.renderTime = endTime - startTime;

    return result;
  }

  /**
   * 渲染单个单元格
   */
  renderCell(cell: CellData, config: MatrixConfig): CellRenderData {
    const handler = modeHandlers[config.mode];
    if (!handler) {
      throw new Error(`Unknown business mode: ${config.mode}`);
    }

    return handler.renderCell(cell, config);
  }

  /**
   * 处理交互事件
   */
  handleInteraction(event: InteractionEvent, cell: CellData, config: MatrixConfig): void {
    const handler = modeHandlers[config.mode];
    if (!handler) {
      throw new Error(`Unknown business mode: ${config.mode}`);
    }

    handler.handleInteraction(event, cell);
  }

  /**
   * 切换业务模式
   */
  switchMode(mode: BusinessMode, data: MatrixData, config: MatrixConfig): ProcessedMatrixData {
    const newConfig = { ...config, mode };
    return this.processData(data, newConfig);
  }

  /**
   * 批量更新单元格
   */
  batchUpdateCells(
    updates: Array<{ coordinate: Coordinate; data: Partial<CellData> }>,
    data: MatrixData
  ): MatrixData {
    const startTime = performance.now();

    const newData = { ...data };
    const newCells = new Map(data.cells);

    updates.forEach(({ coordinate, data: cellData }) => {
      const key = coordinateKey(coordinate.x, coordinate.y);
      const existingCell = newCells.get(key);

      if (existingCell) {
        newCells.set(key, { ...existingCell, ...cellData });
      }
    });

    newData.cells = newCells;

    const endTime = performance.now();
    this.performanceMetrics.updateTime = endTime - startTime;

    return newData;
  }

  /**
   * 获取性能指标
   */
  getPerformanceMetrics(): PerformanceMetrics {
    return { ...this.performanceMetrics };
  }

  /**
   * 重置性能指标
   */
  resetPerformanceMetrics(): void {
    this.performanceMetrics = {
      renderTime: 0,
      updateTime: 0,
      cacheHitRate: 0,
      memoryUsage: 0,
      frameRate: 60,
    };
  }

  /**
   * 验证矩阵数据
   */
  validateData(data: MatrixData): boolean {
    // 检查数据完整性
    if (!data.cells || !(data.cells instanceof Map)) {
      return false;
    }

    // 检查单元格数据
    for (const [key, cell] of data.cells) {
      if (!this.validateCell(cell)) {
        console.warn(`Invalid cell data at ${key}:`, cell);
        return false;
      }
    }

    return true;
  }

  /**
   * 验证单元格数据
   */
  private validateCell(cell: CellData): boolean {
    return (
      typeof cell.x === 'number' &&
      typeof cell.y === 'number' &&
      cell.x >= 0 && cell.x < 33 &&
      cell.y >= 0 && cell.y < 33 &&
      typeof cell.isActive === 'boolean' &&
      typeof cell.isSelected === 'boolean' &&
      typeof cell.isHovered === 'boolean'
    );
  }
}

// ===== 单例实例 =====

export const matrixCore = new MatrixCore();

// ===== 工具函数 =====

/**
 * 创建交互事件
 */
export const createInteractionEvent = (
  type: InteractionEvent['type'],
  coordinate: Coordinate,
  modifiers: Partial<InteractionEvent['modifiers']> = {},
  data?: any
): InteractionEvent => ({
  type,
  coordinate,
  modifiers: {
    ctrl: false,
    shift: false,
    alt: false,
    ...modifiers,
  },
  data,
});

/**
 * 注册自定义模式处理器
 */
export const registerModeHandler = (mode: string, handler: ModeHandler): void => {
  (modeHandlers as any)[mode] = handler;
};

/**
 * 获取可用的业务模式
 */
export const getAvailableModes = (): BusinessMode[] => {
  return Object.keys(modeHandlers) as BusinessMode[];
};
